import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the current OU from query parameter or fall back to user's selected OU
    const { searchParams } = new URL(request.url);
    const currentOuId = searchParams.get('ouId') || session.user.selectedOuId;

    // Find the OU object for the current selection
    const currentOu = await prisma.ou.findUnique({
      where: { id: currentOuId },
    });

    if (!currentOu) {
      return NextResponse.json({ error: "OU not found" }, { status: 404 });
    }

    // Get OUs that the user has access to based on current selection
    const ouWithChildren = await getOusBelowOu(currentOu);
    const ouIds = ouWithChildren?.map((ou) => ou.id) || [];

    // Get all locations for the selected OUs
    const locations = await prisma.location.findMany({
      where: {
        ouId: {
          in: ouIds,
        },
      },
      include: {
        powerContract: {
          where: {
            // Only include active contracts (current date is between start and end)
            start: {
              lte: new Date(),
            },
            end: {
              gte: new Date(),
            },
          },
        },
        evses: {
          select: {
            evse_id: true,
            status: true,
          },
        },
        ou: {
          select: {
            name: true,
            code: true,
          },
        },
      },
    });

    // Filter locations without active power contracts
    const locationsWithoutContracts = locations.filter(location => 
      location.powerContract.length === 0
    );

    // Get recent CDRs for locations without contracts to assess impact
    const locationIdsWithoutContracts = locationsWithoutContracts.map(loc => loc.id);
    
    let recentCdrsCount = 0;
    let recentCdrsVolume = 0;
    
    if (locationIdsWithoutContracts.length > 0) {
      // Get CDRs from the last 30 days for locations without contracts
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentCdrs = await prisma.cdr.findMany({
        where: {
          Location_ID: {
            in: locationIdsWithoutContracts,
          },
          End_datetime: {
            gte: thirtyDaysAgo,
          },
        },
        select: {
          CDR_ID: true,
          Volume: true,
          Location_ID: true,
        },
      });

      recentCdrsCount = recentCdrs.length;
      recentCdrsVolume = recentCdrs.reduce((sum, cdr) => sum + (cdr.Volume || 0), 0);
    }

    // Calculate severity
    const totalLocations = locations.length;
    const locationsWithoutContractsCount = locationsWithoutContracts.length;
    const percentageWithoutContracts = totalLocations > 0 
      ? Math.round((locationsWithoutContractsCount / totalLocations) * 100) 
      : 0;

    // Determine warning level
    let warningLevel: 'none' | 'low' | 'medium' | 'high' = 'none';
    if (locationsWithoutContractsCount > 0) {
      if (recentCdrsCount > 50 || percentageWithoutContracts > 50) {
        warningLevel = 'high';
      } else if (recentCdrsCount > 10 || percentageWithoutContracts > 20) {
        warningLevel = 'medium';
      } else {
        warningLevel = 'low';
      }
    }

    return NextResponse.json({
      status: "success",
      data: {
        warningLevel,
        summary: {
          totalLocations,
          locationsWithoutContracts: locationsWithoutContractsCount,
          percentageWithoutContracts,
          recentCdrsCount,
          recentCdrsVolume: Math.round(recentCdrsVolume * 100) / 100,
        },
        locationsWithoutContracts: locationsWithoutContracts.map(location => ({
          id: location.id,
          name: location.name,
          city: location.city,
          ouName: location.ou.name,
          ouCode: location.ou.code,
          evseCount: location.evses.length,
          activeEvses: location.evses.filter(evse => evse.status === 'AVAILABLE').length,
        })),
      },
    });
  } catch (error) {
    console.error("Error checking power contract warnings:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
